import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { CommentGenerationRequest, CommentGenerationResponse } from '@/types';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json<CommentGenerationResponse>({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json<CommentGenerationResponse>({
        success: false,
        message: '认证令牌无效或已过期'
      }, { status: 401 });
    }

    // 检查用户Token限制（管理员不受限制）
    if (payload.userId !== 'admin') {
      const { data: user, error: userError } = await supabaseAdmin
        .from('users')
        .select('token_limit, is_active')
        .eq('id', payload.userId)
        .single();

      if (userError || !user) {
        return NextResponse.json<CommentGenerationResponse>({
          success: false,
          message: '用户不存在'
        }, { status: 404 });
      }

      // 检查用户是否被禁用
      if (!user.is_active) {
        return NextResponse.json<CommentGenerationResponse>({
          success: false,
          message: '您的账户已被禁用，请联系管理员'
        }, { status: 403 });
      }

      // 检查Token限制（只有当token_limit不为null时才检查）
      if (user.token_limit !== null && user.token_limit !== undefined) {
        // 获取用户已使用的Token数量
        const { data: tokenUsage, error: tokenError } = await supabaseAdmin
          .from('token_usage')
          .select('tokens_used')
          .eq('user_id', payload.userId);

        if (tokenError) {
          console.error('获取Token使用记录失败:', tokenError);
          return NextResponse.json<CommentGenerationResponse>({
            success: false,
            message: '获取Token使用记录失败'
          }, { status: 500 });
        }

        const totalUsed = tokenUsage?.reduce((sum, record) => sum + record.tokens_used, 0) || 0;

        console.log(`用户 ${payload.userId} Token检查: 已使用=${totalUsed}, 限制=${user.token_limit}`);

        if (totalUsed >= user.token_limit) {
          return NextResponse.json<CommentGenerationResponse>({
            success: false,
            message: `您的Token使用量已达到限制（${user.token_limit}），当前已使用${totalUsed}，请联系管理员`
          }, { status: 403 });
        }
      }
    }

    // 解析请求体
    const body: CommentGenerationRequest = await request.json();
    const { studentInfo } = body;

    // 验证学生信息
    if (!studentInfo || !studentInfo.studentName) {
      return NextResponse.json<CommentGenerationResponse>({
        success: false,
        message: '学生信息不完整'
      }, { status: 400 });
    }

    // 构建AI提示词
    const prompt = buildPrompt(studentInfo);

    // 调用Deepseek API
    const deepseekResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!deepseekResponse.ok) {
      const errorText = await deepseekResponse.text();
      console.error('Deepseek API error:', errorText);
      return NextResponse.json<CommentGenerationResponse>({
        success: false,
        message: 'AI服务暂时不可用，请稍后重试'
      }, { status: 500 });
    }

    const deepseekData = await deepseekResponse.json();
    const generatedComment = deepseekData.choices[0]?.message?.content;
    const tokensUsed = deepseekData.usage?.total_tokens || 0;

    if (!generatedComment) {
      return NextResponse.json<CommentGenerationResponse>({
        success: false,
        message: '评语生成失败，请重试'
      }, { status: 500 });
    }

    // 再次检查Token限制（因为现在知道了实际使用的Token数量）
    if (payload.userId !== 'admin') {
      const { data: user } = await supabaseAdmin
        .from('users')
        .select('token_limit')
        .eq('id', payload.userId)
        .single();

      if (user?.token_limit !== null && user?.token_limit !== undefined) {
        const { data: tokenUsage } = await supabaseAdmin
          .from('token_usage')
          .select('tokens_used')
          .eq('user_id', payload.userId);

        const totalUsed = tokenUsage?.reduce((sum, record) => sum + record.tokens_used, 0) || 0;

        // 检查加上本次使用后是否会超过限制
        if (totalUsed + tokensUsed > user.token_limit) {
          console.log(`用户 ${payload.userId} 本次使用后将超过限制: 当前=${totalUsed}, 本次=${tokensUsed}, 限制=${user.token_limit}`);
          return NextResponse.json<CommentGenerationResponse>({
            success: false,
            message: `本次操作将超过Token限制。当前已使用${totalUsed}，本次需要${tokensUsed}，限制为${user.token_limit}`
          }, { status: 403 });
        }
      }

      // 记录Token使用情况
      await supabaseAdmin
        .from('token_usage')
        .insert({
          user_id: payload.userId,
          tokens_used: tokensUsed,
          api_call_type: 'comment_generation',
          created_at: new Date().toISOString()
        });
    }

    // 保存生成的评语到comments表
    await supabaseAdmin
      .from('comments')
      .insert({
        user_id: payload.userId,
        student_name: studentInfo.studentName,
        student_info: studentInfo,
        generated_comment: generatedComment,
        tokens_used: tokensUsed
      });

    return NextResponse.json<CommentGenerationResponse>({
      success: true,
      message: '评语生成成功',
      comment: generatedComment,
      tokensUsed
    });

  } catch (error) {
    console.error('Comment generation error:', error);
    return NextResponse.json<CommentGenerationResponse>({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

function buildPrompt(studentInfo: any): string {
  // 根据选择的语气设置相应的语言风格指导
  const toneGuidance = getToneGuidance(studentInfo.commentTone);

  return `请扮演一位经验丰富、富有同情心、真正关心每一个学生的班主任。你的任务是根据我将提供给你的每位学生的详细结构化数据，为学生生成一份针对当前学期或学年的个性化、全面且具有发展性的评语。

学生信息：
- 学生姓名：${studentInfo.studentName}
- 学生性别：${studentInfo.studentGender}
- 学期/学年：${studentInfo.termYear}
- 主要学科成绩：${studentInfo.majorSubjectPerformance}
- 学科强项分析：${studentInfo.subjectStrengths}
- 学科薄弱点分析：${studentInfo.subjectWeaknesses}
- 学习潜力评估：${studentInfo.learningPotential}
- 对学科的兴趣程度：${studentInfo.subjectInterest}
- 课堂专注度：${studentInfo.classroomConcentration}
- 作业完成情况：${studentInfo.homeworkCompletion}
- 学习主动性与自觉性：${studentInfo.learningProactiveness}
- 遵守纪律与规章：${studentInfo.disciplineCompliance}
- 待人接物态度：${studentInfo.attitudeTowardsOthers}
- 责任心：${studentInfo.responsibility}
- 特长与兴趣：${studentInfo.talentsAndInterests}
- 班干职位：${studentInfo.classPosition}
- 获奖情况：${studentInfo.awards}
- 教师总体评价：${studentInfo.overallAssessment}
- 未来发展期望：${studentInfo.futureExpectations}
- 针对性改进建议/鼓励方向：${studentInfo.improvementSuggestions}

评语要求：
1. 根据评语人称"${studentInfo.commentPerspective}"来称呼学生
2. 评语长度控制在${studentInfo.wordCountRange}字
3. ${toneGuidance}
4. 积极正面为主，委婉地指出需要改进的方面
5. 针对性强，避免泛泛之谈
6. 体现发展性视角，既肯定优点也指出改进方向

请直接输出生成的学生评语文本内容，不需要包含任何额外的标题、说明或格式标记。`;
}

function getToneGuidance(tone: string): string {
  switch (tone) {
    case '温和亲切':
      return '语言温和亲切、充满关爱，如慈祥的长辈对晚辈的关怀，用词温暖，语调柔和，体现出对学生的深切关心和理解';
    case '严谨正式':
      return '语言严谨正式、条理清晰，如专业教育工作者的评价，用词准确，逻辑严密，体现出专业性和权威性';
    case '鼓励激励':
      return '语言充满鼓励和激励，积极向上、富有感染力，如教练对运动员的激励，用词振奋人心，能够激发学生的斗志和潜能';
    case '客观中性':
      return '语言客观中性、实事求是，如研究者的客观分析，用词准确客观，避免过于情感化的表达，注重事实描述';
    default:
      return '语言亲切自然、真诚恳切、具体形象、生动有趣';
  }
}
